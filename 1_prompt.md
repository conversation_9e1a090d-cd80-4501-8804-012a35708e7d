# Prompt: Fix Continuous Drag-and-Drop Movement

## 1. Objective & Context

The objective is to fix a critical bug in the "Reorder Habits" screen's drag-and-drop functionality. Currently, a user can only drag an item one position at a time. For example, to move an item from the top of a list of three to the bottom, they must perform two separate drag actions. The drag action incorrectly terminates after a single swap.

The desired behavior is a **continuous drag gesture**, where the selected habit follows the user's finger smoothly across the entire list, allowing them to move an item from the top to the bottom (or any other position) in a single, uninterrupted motion.

**Root Cause Analysis:**
This "single-step" issue is a classic symptom of an incorrectly implemented drag-and-drop callback. The method responsible for handling the move event is likely not updating the item's position in the underlying data list correctly and consistently throughout the entire gesture. The logic is treating each swap as a final, completed action rather than one part of a continuous movement.

## 2. Detailed Implementation Plan

The fix is centered on refining the logic within the drag-and-drop callback to correctly handle a continuous drag.

**Task 2.1: Review and Refactor the Move-Handling Method**

- Locate the method within the drag-and-drop callback implementation that is responsible for handling the item's movement (often named `onMove`).
- The core logic of this method must perform two actions **every time it is called** during a single drag:
  1.  It must update the position of the dragged item within the backing data list (the list that feeds the adapter). A common way to do this is to remove the item from its starting position and re-insert it at its new target position.
  2.  After updating the data list, it must call the adapter's method to notify the user interface that an item has moved from the start to the end position.

By ensuring the data source is always in sync with the visual representation during the entire drag, the system can correctly calculate the item's path and allow it to move freely across multiple positions.

**Task 2.2: Ensure Drag is Enabled by Long Press**

- Verify that the drag-and-drop callback is configured to allow dragging to be initiated by a long press. The method that controls this behavior (often named `isLongPressDragEnabled`) should be set to return `true`.

## 3. Verification Plan

Please follow these steps meticulously to confirm the fix.

1.  Navigate to the "Reorder Habits" screen with at least three items.
2.  Long-press the top item.
3.  **Without releasing your finger**, drag the item downwards over the second item and continue dragging it over the third item, all the way to the bottom of the list.
4.  **Verify (CRITICAL):** The selected item must follow your finger smoothly and continuously across the entire list. It should not stop after swapping with the second item.
5.  Release the item at the bottom position.
6.  **Verify:** The list settles correctly with the new order.
7.  Now, long-press the new bottom item and drag it all the way to the top in a single, uninterrupted motion.
8.  **Verify:** The drag is again smooth and continuous, and the item settles correctly at the top.

## 4. Mandatory Development Guidelines

- **Refer to the Style Guide:** Before starting any feature, always consult the project's style guide for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns. It is the single source of truth for styling decisions.
- **Study the Reference Project:** Prior to implementation, review the reference project to understand how similar features have been approached to maintain consistency and avoid duplications or contradictions. The reference project serves as a blueprint for implementation. This step is mandatory. Do not proceed to implementation without this step.The reference project is located in the `uhabits-dev` folder in the root directory.
- **Understand the Existing Project Structure:** Before writing any code, spend time exploring and understanding how the current system is structured. Even for new features, existing components or utility functions may be reusable. Integrate changes cleanly into the existing architecture instead of creating disconnected code.
- **Maintain a Clean Codebase:** After implementing features, remove any temporary, test, or duplicate files, folders, or unused components that were created during development. Keep the codebase organized and clutter-free.
- **Pause If There Is Any Confusion:** If at any point the requirements are unclear, do not proceed based on assumptions. Immediately pause and seek clarification. It is better to get clarity than to redo or fix avoidable mistakes later.
- **Remove Unused Old Implementations:** As part of final review, identify and delete any old, unused code that was implemented earlier but is no longer in use. This includes obsolete modules, features, or legacy logic.
