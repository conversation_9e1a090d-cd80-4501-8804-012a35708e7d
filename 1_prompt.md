# Prompt: Fix Habit Reverting After Drag-and-Drop

## 1. Objective & Context

The objective is to fix a critical bug on the "Reorder Habits" screen. When a user drags a habit to a new position and releases it, the habit visually reverts to its original position instead of staying in the new one, as shown in the scenario from `56.jpg`. The reordering action fails to complete successfully.

**Root Cause Analysis:**
The current implementation correctly handles the visual movement of the item *during* the drag gesture. However, it is missing the crucial final step: updating the underlying data source when the drag action is completed. The drag-and-drop callback system has a method that is specifically designed to handle the "cleanup" or "finalization" of a drag action. Because this step is missing, the list's official state is never updated, and the UI snaps back to reflect the last known valid order.

## 2. Detailed Implementation Plan

The fix requires adding logic to the method that is called when a drag action is completed and the item view is released.

**Task 2.1: Implement the Drag Completion Callback**
- In the drag-and-drop callback implementation for the reordering screen, locate and override the method that is called when a dragged item is released (it is often named `clearView` or a similar name indicating the view state is being reset). This method is the designated place to finalize any changes.
- Inside this method, you must perform the following actions:
    1.  First, call the `super` implementation of the method to ensure all default animations and state clearing occur correctly.
    2.  After the `super` call, get the final, updated list of habits from the list's data adapter.
    3.  Pass this complete, reordered list to the state management component (e.g., `ViewModel`).
    4.  The state management component must then trigger the logic to save the new order to the database.

This ensures that as soon as the user drops the item, the new order is immediately treated as the source of truth and saved.

## 3. Verification Plan

Please follow these steps meticulously to confirm the fix.

1.  Navigate to the "Reorder Habits" screen with a list of habits, as seen in `56.jpg`.
2.  Long-press the second item in the list and drag it to the bottom.
3.  Release your finger.
4.  **Verify (CRITICAL):** The item you moved must **stay** at the bottom of the list. It must not revert to its original position.
5.  Now, drag the top item and drop it in the middle of the list.
6.  **Verify:** The item stays in its new position.
7.  Press the back button to return to the main habits screen.
8.  **Verify:** The main screen reflects this final, correct order.

## 4. Mandatory Development Guidelines

- **Refer to the Style Guide:** Before starting any feature, always consult the project's style guide for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns. It is the single source of truth for styling decisions.
- **Study the Reference Project:** Prior to implementation, review the reference project to understand how similar features have been approached to maintain consistency and avoid duplications or contradictions. The reference project serves as a blueprint for implementation. This step is mandatory. Do not proceed to implementation without this step.
- **Understand the Existing Project Structure:** Before writing any code, spend time exploring and understanding how the current system is structured. Even for new features, existing components or utility functions may be reusable. Integrate changes cleanly into the existing architecture instead of creating disconnected code.
- **Maintain a Clean Codebase:** After implementing features, remove any temporary, test, or duplicate files, folders, or unused components that were created during development. Keep the codebase organized and clutter-free.
- **Pause If There Is Any Confusion:** If at any point the requirements are unclear, do not proceed based on assumptions. Immediately pause and seek clarification. It is better to get clarity than to redo or fix avoidable mistakes later.
- **Remove Unused Old Implementations:** As part of final review, identify and delete any old, unused code that was implemented earlier but is no longer in use. This includes obsolete modules, features, or legacy logic.