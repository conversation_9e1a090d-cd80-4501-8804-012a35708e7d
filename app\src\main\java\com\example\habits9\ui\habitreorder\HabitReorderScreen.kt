package com.example.habits9.ui.habitreorder

import com.example.habits9.data.Habit
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateOffsetAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectDragGesturesAfterLongPress
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.zIndex
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.DragHandle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.compositeOver
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.compose.foundation.isSystemInDarkTheme
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

// Simplified color approach using Material3 theme colors

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitReorderScreen(
    onBackClick: () -> Unit,
    viewModel: HabitReorderViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val hapticFeedback = LocalHapticFeedback.current

    // Save and clear state when navigating back to ensure persistence
    val handleBackClick = {
        viewModel.saveOrder()
        viewModel.clearReorderedState()
        onBackClick()
    }

    // Save and clear state when screen is disposed (e.g., system back button)
    DisposableEffect(Unit) {
        onDispose {
            viewModel.saveOrder()
            viewModel.clearReorderedState()
        }
    }

    // Enhanced drag state for smoother experience with settling animation
    var draggedItemIndex by remember { mutableStateOf(-1) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    var targetDropIndex by remember { mutableStateOf(-1) }
    var isDragging by remember { mutableStateOf(false) }
    var isSettling by remember { mutableStateOf(false) }  // New state for settlement animation
    val listState = rememberLazyListState()

    // Track the original dragged item for continuous dragging
    var originalDraggedItem by remember { mutableStateOf<Habit?>(null) }

    // Smooth animated drag offset for better finger tracking
    val animatedDragOffset by animateOffsetAsState(
        targetValue = dragOffset,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioNoBouncy,
            stiffness = Spring.StiffnessHigh
        ),
        label = "dragOffset"
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Reorder Habits",
                        color = MaterialTheme.colorScheme.onSurface,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = handleBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            )
        },
        containerColor = MaterialTheme.colorScheme.background
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (uiState.habits.isEmpty()) {
                // Empty state
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "You have no habits to organize.",
                        color = MaterialTheme.colorScheme.onSurface,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Go back and add a new habit to get started!",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontSize = 14.sp
                    )
                }
            } else {
                // Habit list
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    itemsIndexed(
                        items = uiState.habits,
                        key = { index, habit -> "${habit.uuid}_reorder_$index" }
                    ) { index, habit ->
                        val isDraggingThisItem = draggedItemIndex == index

                        // Enhanced displacement calculation for fluid item movement
                        val displacement = when {
                            isDraggingThisItem -> 0f
                            isDragging && draggedItemIndex != -1 -> {
                                // More responsive displacement that tracks finger position
                                val originalIndex = draggedItemIndex
                                val currentIndex = index

                                // Calculate displacement based on target drop position
                                when {
                                    // Item should move up (negative displacement)
                                    originalIndex < currentIndex && targetDropIndex >= currentIndex -> -1f
                                    // Item should move down (positive displacement)
                                    originalIndex > currentIndex && targetDropIndex <= currentIndex -> 1f
                                    // Smooth transition for items near the drag position
                                    targetDropIndex == currentIndex && !isDraggingThisItem -> {
                                        // Create gap at target position
                                        if (originalIndex < currentIndex) -1f else 1f
                                    }
                                    else -> 0f
                                }
                            }
                            else -> 0f
                        }

                        val animatedDisplacement by animateFloatAsState(
                            targetValue = displacement,
                            animationSpec = spring(
                                dampingRatio = Spring.DampingRatioNoBouncy,  // No bounce for smoother feel
                                stiffness = Spring.StiffnessHigh  // Higher stiffness for more responsive tracking
                            ),
                            label = "displacement_$index"
                        )

                        HabitReorderItem(
                            habitName = habit.name,
                            isDragging = isDraggingThisItem,
                            isSettling = isSettling && isDraggingThisItem,  // Pass settling state
                            dragOffset = if (isDraggingThisItem) animatedDragOffset else Offset.Zero,
                            isDropTarget = targetDropIndex == index && !isDraggingThisItem,
                            displacement = animatedDisplacement,
                            onDragStart = {
                                draggedItemIndex = index
                                originalDraggedItem = habit  // Store the original item being dragged
                                dragOffset = Offset.Zero
                                targetDropIndex = -1
                                isDragging = true
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            },
                            onDrag = { dragAmount ->
                                dragOffset += dragAmount

                                // Enhanced drop target calculation for fluid finger tracking
                                val visibleItems = listState.layoutInfo.visibleItemsInfo
                                val currentItemInfo = visibleItems.find { it.index == draggedItemIndex }

                                if (currentItemInfo != null) {
                                    val itemCenterY = currentItemInfo.offset + currentItemInfo.size / 2 + dragOffset.y

                                    // Find the item we're hovering over
                                    var hoveredItem: androidx.compose.foundation.lazy.LazyListItemInfo? = null
                                    for (item in visibleItems) {
                                        if (item.index == draggedItemIndex) continue

                                        val itemTop = item.offset
                                        val itemBottom = item.offset + item.size

                                        if (itemCenterY >= itemTop && itemCenterY <= itemBottom) {
                                            hoveredItem = item
                                            break
                                        }
                                    }

                                    // Calculate the correct insertion position
                                    val newTargetIndex = if (hoveredItem != null) {
                                        val itemTop = hoveredItem.offset
                                        val itemBottom = hoveredItem.offset + hoveredItem.size
                                        val itemMiddle = itemTop + hoveredItem.size / 2

                                        // Determine insertion position based on which half we're in
                                        if (itemCenterY <= itemMiddle) {
                                            // Top half - insert before this item
                                            hoveredItem.index
                                        } else {
                                            // Bottom half - insert after this item
                                            hoveredItem.index + 1
                                        }
                                    } else -1

                                    // Update target drop index for visual feedback only
                                    if (newTargetIndex != targetDropIndex) {
                                        targetDropIndex = newTargetIndex
                                        if (newTargetIndex != -1) {
                                            hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                        }
                                    }

                                    // DO NOT update data model during drag - this was the root cause
                                    // Only track visual feedback, actual reorder happens on drag end
                                }
                            },
                            onDragEnd = {
                                // Perform the actual reorder if there's a valid target
                                if (targetDropIndex != -1 && targetDropIndex != draggedItemIndex) {
                                    viewModel.moveHabit(draggedItemIndex, targetDropIndex)
                                }

                                // Enhanced settlement animation
                                isSettling = true
                                isDragging = false

                                // Use coroutine for smooth settlement timing
                                CoroutineScope(Dispatchers.Main).launch {
                                    // Allow animations to complete before clearing state
                                    delay(200)  // Slightly longer than animation duration

                                    draggedItemIndex = -1
                                    dragOffset = Offset.Zero
                                    targetDropIndex = -1
                                    originalDraggedItem = null
                                    isSettling = false
                                    viewModel.saveOrder()
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun HabitReorderItem(
    habitName: String,
    isDragging: Boolean,
    isSettling: Boolean = false,  // New parameter for settling state
    dragOffset: Offset,
    isDropTarget: Boolean = false,
    displacement: Float = 0f,
    onDragStart: () -> Unit,
    onDrag: (Offset) -> Unit,
    onDragEnd: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Enhanced visual feedback with precise timing and settling animation
    val animatedElevation by animateDpAsState(
        targetValue = when {
            isDragging -> 16.dp  // Noticeable elevation shadow for lift effect
            isSettling -> 2.dp   // Animate back to normal during settling
            isDropTarget -> 8.dp
            else -> 2.dp
        },
        animationSpec = when {
            isSettling -> tween(
                durationMillis = 200,  // Slightly longer for smooth settlement
                easing = FastOutSlowInEasing
            )
            else -> tween(
                durationMillis = 150,  // 150ms as specified in prompt
                easing = FastOutSlowInEasing
            )
        },
        label = "elevation"
    )

    val animatedScale by animateFloatAsState(
        targetValue = when {
            isDragging -> 1.05f  // Exactly 1.05x as specified in prompt
            isSettling -> 1f     // Animate back to normal scale during settling
            isDropTarget -> 1.02f
            else -> 1f
        },
        animationSpec = when {
            isSettling -> tween(
                durationMillis = 200,  // Smooth settlement animation
                easing = FastOutSlowInEasing
            )
            else -> tween(
                durationMillis = 150,  // 150ms as specified in prompt
                easing = FastOutSlowInEasing
            )
        },
        label = "scale"
    )

    val animatedAlpha by animateFloatAsState(
        targetValue = when {
            isDragging -> 0.95f
            isSettling -> 1f     // Return to full opacity during settling
            isDropTarget -> 0.85f
            else -> 1f
        },
        animationSpec = when {
            isSettling -> tween(
                durationMillis = 200,  // Consistent settlement timing
                easing = FastOutSlowInEasing
            )
            else -> tween(
                durationMillis = 150,  // Consistent timing
                easing = FastOutSlowInEasing
            )
        },
        label = "alpha"
    )

    // Enhanced background color animation with Material3 surface-variant support
    val animatedBackgroundColor by animateColorAsState(
        targetValue = when {
            isDragging -> MaterialTheme.colorScheme.surfaceVariant  // Use Material3 surface-variant color
            isSettling -> MaterialTheme.colorScheme.surfaceVariant  // Return to normal during settling
            isDropTarget -> MaterialTheme.colorScheme.primary.copy(alpha = 0.08f).compositeOver(MaterialTheme.colorScheme.surfaceVariant)
            else -> MaterialTheme.colorScheme.surfaceVariant
        },
        animationSpec = when {
            isSettling -> tween(
                durationMillis = 200,  // Smooth color transition during settlement
                easing = FastOutSlowInEasing
            )
            else -> tween(
                durationMillis = 150,  // 150ms as specified in prompt
                easing = FastOutSlowInEasing
            )
        },
        label = "backgroundColor"
    )

    Card(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = animatedElevation,
                shape = RoundedCornerShape(12.dp)
            )
            .graphicsLayer {
                // Hardware-accelerated smooth finger tracking and displacement
                translationX = dragOffset.x
                translationY = dragOffset.y + (displacement * 80.dp.toPx()) // 80dp is approximate item height
                scaleX = animatedScale
                scaleY = animatedScale
                alpha = animatedAlpha
            }
            .zIndex(if (isDragging) 10f else if (isDropTarget) 5f else 1f)
            .pointerInput(Unit) {
                detectDragGesturesAfterLongPress(
                    onDragStart = { offset ->
                        onDragStart()
                    },
                    onDrag = { change, dragAmount ->
                        change.consume()
                        onDrag(dragAmount)
                    },
                    onDragEnd = {
                        onDragEnd()
                    },
                    onDragCancel = {
                        onDragEnd()
                    }
                )
            },
        colors = CardDefaults.cardColors(
            containerColor = animatedBackgroundColor  // Use animated background color
        ),
        shape = RoundedCornerShape(12.dp),
        border = when {
            isDragging -> BorderStroke(2.dp, MaterialTheme.colorScheme.primary.copy(alpha = 0.4f))
            isDropTarget -> BorderStroke(1.dp, MaterialTheme.colorScheme.primary.copy(alpha = 0.2f))
            else -> null
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Enhanced drag handle with Material3 colors
            Icon(
                imageVector = Icons.Default.DragHandle,
                contentDescription = "Drag to reorder",
                tint = when {
                    isDragging -> MaterialTheme.colorScheme.primary
                    isDropTarget -> MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                    else -> MaterialTheme.colorScheme.onSurfaceVariant
                },
                modifier = Modifier
                    .size(24.dp)
                    .padding(2.dp)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = habitName,
                color = when {
                    isDragging -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.95f)
                    isDropTarget -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f)
                    else -> MaterialTheme.colorScheme.onSurface
                },
                fontSize = 16.sp,
                fontWeight = when {
                    isDragging -> FontWeight.SemiBold
                    isDropTarget -> FontWeight.Medium
                    else -> FontWeight.Medium
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
}
